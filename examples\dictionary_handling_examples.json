[{"description": "Single-key dict with registered deserializer", "input": [{"ListNode": [1, 2, 3]}]}, {"description": "Single-key dict with unregistered key (treated as regular dict)", "input": [{"CustomClass": [1, 2, 3]}]}, {"description": "Multi-key regular dictionary (always treated as regular dict)", "input": [{"key": "value", "count": 5}]}, {"description": "Mixed nested structure", "input": [{"nodes": [{"ListNode": [1, 2]}, {"ListNode": [3, 4]}], "metadata": {"count": 2, "type": "linked_list"}}]}, {"description": "List with mixed dictionary types", "input": [[{"ListNode": [1, 2, 3]}, {"key": "value"}, {"TreeNode": [5]}]]}]